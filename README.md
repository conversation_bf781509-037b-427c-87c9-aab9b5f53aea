# MediaTrack - 集中式文件同步系统

MediaTrack 是一个基于 Electron 的桌面应用程序，用于实现集中式文件同步和协作管理。

## 功能特性

### 已实现功能
- ✅ 项目创建（新建项目）
- ✅ 项目导入（导入现有项目）
- ✅ 项目列表展示
- ✅ 项目重命名
- ✅ 项目设置管理
- ✅ 项目元数据存储
- ✅ 项目搜索和筛选
- ✅ 深色主题UI

### 待开发功能
- ⏳ 用户登录系统
- ⏳ 文件浏览和操作
- ⏳ 实时同步功能
- ⏳ 成员管理
- ⏳ 权限控制
- ⏳ 通知系统
- ⏳ 文件监控直播

## 技术栈

- **Electron** v27.x - 跨平台桌面应用框架
- **Vue.js** v3.x - 前端框架
- **Tailwind CSS** v3.x - CSS框架
- **Node.js** v18.x - JavaScript运行环境

## 项目结构

```
ccc/
├── docs/                      # 文档目录
│   ├── 项目需求清单.md
│   ├── 项目文件结构文档.md
│   └── 技术栈框架文档.md
├── src/                       # 源代码目录
│   ├── main/                  # 主进程代码
│   │   ├── index.js          # 主进程入口
│   │   └── ipc/              # IPC通信模块
│   ├── renderer/             # 渲染进程代码
│   │   ├── index.html        # 主页面
│   │   ├── js/               # JavaScript文件
│   │   └── styles/           # 样式文件
│   └── preload/              # 预加载脚本
├── package.json              # 项目配置
└── README.md                 # 项目说明
```

## 安装与运行

### 前置要求

- Node.js v18.0.0 或更高版本
- npm 或 yarn 包管理器
- Windows 10/11 操作系统

### 安装步骤

1. 克隆或下载项目到本地

2. 进入项目目录
```bash
cd ccc
```

3. 安装依赖
```bash
npm install
```

### 运行应用

开发模式运行：
```bash
npm run dev
```

生产模式运行：
```bash
npm start
```

### 打包应用

打包为Windows安装程序：
```bash
npm run build
```

打包后的安装程序将生成在 `dist` 目录中。

## 使用说明

### 创建新项目

1. 点击顶部的"新建项目"按钮
2. 输入项目名称
3. 选择项目存储位置
4. 填写项目描述（可选）
5. 点击"创建项目"

### 导入现有项目

1. 点击"新建项目"按钮
2. 选择"导入现有项目"选项
3. 选择要导入的项目文件夹
4. 添加项目描述（可选）
5. 点击"导入项目"

### 项目管理

- **重命名项目**：在项目列表中点击项目卡片右上角的菜单，选择"重命名"
- **项目设置**：在项目列表中点击项目卡片右上角的菜单，选择"项目设置"
- **搜索项目**：使用顶部搜索框快速查找项目
- **筛选项目**：使用状态筛选按钮查看不同状态的项目

## 数据存储

- **项目列表数据**：`%APPDATA%/MediaTrack/projects.json`
- **项目元数据**：`[项目目录]/.mediatrack/`
  - `config.json` - 项目配置文件
  - `cache/` - 缓存目录
  - `logs/` - 日志目录
  - `thumbnails/` - 缩略图目录

## 开发说明

### 项目命名规范

- 文件命名：小写字母，连字符分隔
- 变量命名：驼峰命名法
- 组件命名：首字母大写驼峰
- CSS类名：连字符分隔

### IPC通信频道

- `project:create` - 创建项目
- `project:import` - 导入项目
- `project:rename` - 重命名项目
- `project:update-settings` - 更新项目设置
- `dialog:select-directory` - 选择目录对话框

## 注意事项

1. 项目名称不能包含特殊字符：`\ / : * ? " < > |`
2. 项目名称长度限制为50个字符
3. 重命名项目会同时更改本地文件夹名称
4. 项目文件夹被删除后，需要重新指定项目目录

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系 MediaTrack 开发团队。 